/**
 * Centralized SMS Template Service
 * Single source of truth for all SMS templates
 */

const { Template, DefaultLanguageSetting } = require('../models');
const DateUtils = require('../utils/dateUtils');

class SmsTemplateService {
  // Template types enum
  static TEMPLATE_TYPES = {
    PAYMENT_REMINDER: 'payment_reminder',
    CONTRACT_REMINDER: 'contract_reminder',
    PAYMENT_DISPLAY_REMINDER: 'payment_display_reminder',
    CONTRACT_DISPLAY_REMINDER: 'contract_display_reminder',
    PAYMENT_DISPLAY_CONSOLIDATED_REMINDER: 'payment_display_consolidated_reminder',
    CONTRACT_DISPLAY_CONSOLIDATED_REMINDER: 'contract_display_consolidated_reminder'
  };

  /**
   * Get template by type and language
   * @param {string} templateType - Type of template
   * @param {string} language - Language code ('en' or 'am')
   * @returns {string} Template string
   */
  static async getTemplate(templateType, language = 'en') {
    try {
      const templates = await Template.findAll();
      
      let template = null;
      switch (templateType) {
        case this.TEMPLATE_TYPES.PAYMENT_REMINDER:
          template = templates.find(t => t.name === 'Payment Reminder');
          break;
        case this.TEMPLATE_TYPES.CONTRACT_REMINDER:
          template = templates.find(t => t.name === 'Contract Reminder');
          break;
        case this.TEMPLATE_TYPES.PAYMENT_DISPLAY_REMINDER:
          template = templates.find(t => t.name === 'Payment Display Reminder');
          break;
        case this.TEMPLATE_TYPES.CONTRACT_DISPLAY_REMINDER:
          template = templates.find(t => t.name === 'Contract Display Reminder');
          break;
        case this.TEMPLATE_TYPES.PAYMENT_DISPLAY_CONSOLIDATED_REMINDER:
          template = templates.find(t => t.name === 'Payment Display Consolidated Reminder');
          break;
        case this.TEMPLATE_TYPES.CONTRACT_DISPLAY_CONSOLIDATED_REMINDER:
          template = templates.find(t => t.name === 'Contract Display Consolidated Reminder');
          break;
        default:
          throw new Error(`Unknown template type: ${templateType}`);
      }

      if (!template) {
        throw new Error(`Template not found for type: ${templateType}`);
      }

      return language === 'am' ? template.template_am : template.template_en;
    } catch (error) {
      throw new Error(`Failed to get template: ${error.message}`);
    }
  }

  /**
   * Create payment reminder message using centralized template
   * @param {object} payment - Payment data
   * @param {string} language - Language code
   * @returns {string} Formatted message
   */
  static async createPaymentReminderMessage(payment, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.PAYMENT_REMINDER, languageCode);

      // Calculate days remaining
      const daysRemaining = DateUtils.calculateDaysRemaining(payment.end_date);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = payment.customer_name_am || payment.customer_name || 'ደንበኛ';
      } else {
        customerName = payment.customer_name || 'Customer';
      }

      const amount = payment.GroundTotal || payment.line_total || 0;

      // Format currency in Ethiopian Birr
      const formattedAmount = DateUtils.formatCurrency(amount, languageCode);

      // Convert end date to Ethiopian calendar
      const ethDate = DateUtils.toEthiopianDate(payment.end_date);
      const formattedDate = DateUtils.formatEthiopianDate(ethDate, languageCode);

      // Get urgency text based on days remaining
      const urgencyText = DateUtils.getUrgencyText(daysRemaining, languageCode);

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{room}/g, payment.room)
        .replace(/{urgencyText}/g, urgencyText)
        .replace(/{formattedDate}/g, formattedDate)
        .replace(/{formattedAmount}/g, formattedAmount)
        .replace(/{description}/g, payment.description || (languageCode === 'am' ? 'ክፍያ' : 'Payment'))
        .replace(/{paymentId}/g, payment.id);

      return message;
    } catch (error) {
      throw new Error(`Failed to create payment reminder message: ${error.message}`);
    }
  }

  /**
   * Create contract reminder message using centralized template
   * @param {object} contract - Contract data
   * @param {string} language - Language code
   * @returns {string} Formatted message
   */
  static async createContractReminderMessage(contract, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.CONTRACT_REMINDER, languageCode);

      // Calculate days remaining
      const daysRemaining = DateUtils.calculateDaysRemaining(contract.EndDate);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = contract.customer_name_am || contract.customer_name || 'ደንበኛ';
      } else {
        customerName = contract.customer_name || 'Customer';
      }

      const roomPrice = contract.RoomPrice || 0;

      // Format currency in Ethiopian Birr
      const formattedPrice = DateUtils.formatCurrency(roomPrice, languageCode);

      // Convert dates to Ethiopian calendar
      const ethEndDate = DateUtils.toEthiopianDate(contract.EndDate);
      const ethStartDate = DateUtils.toEthiopianDate(contract.StartDate);
      const formattedEndDate = DateUtils.formatEthiopianDate(ethEndDate, languageCode);
      const formattedStartDate = DateUtils.formatEthiopianDate(ethStartDate, languageCode);

      // Get urgency text based on days remaining
      const urgencyText = DateUtils.getUrgencyText(daysRemaining, languageCode);

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{room}/g, contract.RoomID)
        .replace(/{roomId}/g, contract.RoomID)
        .replace(/{urgencyText}/g, urgencyText)
        .replace(/{formattedDate}/g, formattedEndDate)
        .replace(/{formattedEndDate}/g, formattedEndDate)
        .replace(/{formattedStartDate}/g, formattedStartDate)
        .replace(/{formattedAmount}/g, formattedPrice)
        .replace(/{formattedPrice}/g, formattedPrice)
        .replace(/{description}/g, contract.description || (languageCode === 'am' ? 'ኪራይ' : 'Rent'))
        .replace(/{contractId}/g, contract.ContractID);

      return message;
    } catch (error) {
      throw new Error(`Failed to create contract reminder message: ${error.message}`);
    }
  }

  /**
   * Create payment display reminder message using centralized template
   * @param {object} payment - Payment data
   * @param {string} language - Language code
   * @returns {string} Formatted message
   */
  static async createPaymentDisplayReminderMessage(payment, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.PAYMENT_DISPLAY_REMINDER, languageCode);

      // Calculate days remaining
      const daysRemaining = DateUtils.calculateDaysRemaining(payment.end_date);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = payment.customer_name_am || payment.customer_name || 'ደንበኛ';
      } else {
        customerName = payment.customer_name || 'Customer';
      }

      const amount = payment.GroundTotal || payment.line_total || 0;

      // Format currency in Ethiopian Birr
      const formattedAmount = DateUtils.formatCurrency(amount, languageCode);

      // Convert end date to Ethiopian calendar
      const ethDate = DateUtils.toEthiopianDate(payment.end_date);
      const formattedDate = DateUtils.formatEthiopianDate(ethDate, languageCode);

      // Get urgency text based on days remaining
      const urgencyText = DateUtils.getUrgencyText(daysRemaining, languageCode);

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{room}/g, payment.room)
        .replace(/{urgencyText}/g, urgencyText)
        .replace(/{formattedDate}/g, formattedDate)
        .replace(/{formattedAmount}/g, formattedAmount)
        .replace(/{description}/g, payment.description || (languageCode === 'am' ? 'ማሳያ ክፍያ' : 'Display payment'))
        .replace(/{paymentId}/g, payment.id);

      return message;
    } catch (error) {
      throw new Error(`Failed to create payment display reminder message: ${error.message}`);
    }
  }

  /**
   * Create contract display reminder message using centralized template
   * @param {object} contract - Contract data
   * @param {string} language - Language code
   * @returns {string} Formatted message
   */
  static async createContractDisplayReminderMessage(contract, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.CONTRACT_DISPLAY_REMINDER, languageCode);

      // Calculate days remaining
      const daysRemaining = DateUtils.calculateDaysRemaining(contract.EndDate);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = contract.customer_name_am || contract.customer_name || 'ደንበኛ';
      } else {
        customerName = contract.customer_name || 'Customer';
      }

      const roomPrice = contract.RoomPrice || 0;

      // Format currency in Ethiopian Birr
      const formattedPrice = DateUtils.formatCurrency(roomPrice, languageCode);

      // Convert dates to Ethiopian calendar
      const ethEndDate = DateUtils.toEthiopianDate(contract.EndDate);
      const ethStartDate = DateUtils.toEthiopianDate(contract.StartDate);
      const formattedEndDate = DateUtils.formatEthiopianDate(ethEndDate, languageCode);
      const formattedStartDate = DateUtils.formatEthiopianDate(ethStartDate, languageCode);

      // Get urgency text based on days remaining
      const urgencyText = DateUtils.getUrgencyText(daysRemaining, languageCode);

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{room}/g, contract.RoomID)
        .replace(/{roomId}/g, contract.RoomID)
        .replace(/{urgencyText}/g, urgencyText)
        .replace(/{formattedDate}/g, formattedEndDate)
        .replace(/{formattedEndDate}/g, formattedEndDate)
        .replace(/{formattedStartDate}/g, formattedStartDate)
        .replace(/{formattedAmount}/g, formattedPrice)
        .replace(/{formattedPrice}/g, formattedPrice)
        .replace(/{description}/g, contract.description || (languageCode === 'am' ? 'ማሳያ ኪራይ' : 'Display rent'))
        .replace(/{contractId}/g, contract.ContractID);

      return message;
    } catch (error) {
      throw new Error(`Failed to create contract display reminder message: ${error.message}`);
    }
  }

  /**
   * Create consolidated payment display reminder message for multiple payments
   * @param {Object} customerGroup - Customer group with multiple payments
   * @param {string} language - Language code ('en' or 'am')
   * @returns {Promise<string>} Formatted SMS message
   */
  static async createConsolidatedPaymentDisplayReminderMessage(customerGroup, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.PAYMENT_DISPLAY_CONSOLIDATED_REMINDER, languageCode);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = customerGroup.customer_name_am || customerGroup.customer_name || 'ደንበኛ';
      } else {
        customerName = customerGroup.customer_name || 'Customer';
      }

      // Format total amount in Ethiopian Birr
      const formattedTotalAmount = DateUtils.formatCurrency(customerGroup.totalAmount, languageCode);

      // Create payment details list (optimized for SMS)
      let paymentDetails = '';
      customerGroup.payments.forEach((payment, index) => {
        const paymentAmount = DateUtils.formatCurrency(payment.GroundTotal || payment.line_total || 0, languageCode);

        if (languageCode === 'am') {
          paymentDetails += `${index + 1}.ክ${payment.room}:${paymentAmount}\n`;
        } else {
          paymentDetails += `${index + 1}.R${payment.room}:${paymentAmount}\n`;
        }
      });

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{paymentCount}/g, customerGroup.paymentCount)
        .replace(/{paymentDetails}/g, paymentDetails.trim())
        .replace(/{formattedTotalAmount}/g, formattedTotalAmount);

      return message;
    } catch (error) {
      throw new Error(`Failed to create consolidated payment display reminder message: ${error.message}`);
    }
  }

  /**
   * Create consolidated contract display reminder message for multiple contracts
   * @param {Object} customerGroup - Customer group with multiple contracts
   * @param {string} language - Language code ('en' or 'am')
   * @returns {Promise<string>} Formatted SMS message
   */
  static async createConsolidatedContractDisplayReminderMessage(customerGroup, language = null) {
    try {
      // Get default language if not specified
      const languageCode = language || await DefaultLanguageSetting.getDefaultLanguageCode();

      // Get template
      const template = await this.getTemplate(this.TEMPLATE_TYPES.CONTRACT_DISPLAY_CONSOLIDATED_REMINDER, languageCode);

      // Use language-specific customer name
      let customerName;
      if (languageCode === 'am') {
        customerName = customerGroup.customer_name_am || customerGroup.customer_name || 'ደንበኛ';
      } else {
        customerName = customerGroup.customer_name || 'Customer';
      }

      // Format total rent in Ethiopian Birr
      const formattedTotalRent = DateUtils.formatCurrency(customerGroup.totalRent, languageCode);

      // Create contract details list (optimized for SMS)
      let contractDetails = '';
      customerGroup.contracts.forEach((contract, index) => {
        const contractRent = DateUtils.formatCurrency(contract.RoomPrice || 0, languageCode);

        if (languageCode === 'am') {
          contractDetails += `${index + 1}.ክ${contract.RoomID}:${contractRent}\n`;
        } else {
          contractDetails += `${index + 1}.R${contract.RoomID}:${contractRent}\n`;
        }
      });

      // Replace template variables
      const message = template
        .replace(/{customerName}/g, customerName)
        .replace(/{contractCount}/g, customerGroup.contractCount)
        .replace(/{contractDetails}/g, contractDetails.trim())
        .replace(/{formattedTotalRent}/g, formattedTotalRent);

      return message;
    } catch (error) {
      throw new Error(`Failed to create consolidated contract display reminder message: ${error.message}`);
    }
  }
}

module.exports = SmsTemplateService;
