/**
 * Test Centralized SMS Template Service
 * Verifies that all services use the same centralized templates
 */

const SmsTemplateService = require('../src/services/smsTemplateService');
const SmsService = require('../src/services/smsService');
const PaymentDeadlineScheduler = require('../src/schedulers/paymentDeadlineScheduler');
const ContractDeadlineScheduler = require('../src/schedulers/contractDeadlineScheduler');
const SmsOptimizer = require('../src/utils/smsOptimizer');

// Sample test data
const samplePayment = {
  id: 'PAY-12345',
  customer_name: '<PERSON>',
  customer_name_am: 'ዮሐንስ ዶ',
  room: '101',
  GroundTotal: 5000,
  end_date: new Date('2024-03-15'),
  description: 'Monthly rent payment'
};

const sampleContract = {
  ContractID: 'CON-67890',
  customer_name: '<PERSON>',
  customer_name_am: 'ዮሐንስ ዶ',
  RoomID: '101',
  RoomPrice: 3500,
  StartDate: new Date('2024-01-01'),
  EndDate: new Date('2024-12-31'),
  description: 'Rental contract'
};

async function testCentralizedTemplates() {
  console.log('='.repeat(80));
  console.log('CENTRALIZED SMS TEMPLATE SERVICE TEST');
  console.log('='.repeat(80));
  console.log(`SMS Limits: ${SmsOptimizer.SMS_LIMITS.ENGLISH} English chars, ${SmsOptimizer.SMS_LIMITS.AMHARIC} Amharic chars`);
  console.log('='.repeat(80));

  try {
    // Test Payment Reminder Templates
    console.log('\n📱 PAYMENT REMINDER TEMPLATES');
    console.log('-'.repeat(50));

    // English Payment Reminder
    const paymentEnglish = await SmsTemplateService.createPaymentReminderMessage(samplePayment, 'en');
    const paymentEnglishAnalysis = SmsOptimizer.analyzeText(paymentEnglish);
    console.log(`\n✅ Payment Reminder (English)`);
    console.log(`Characters: ${paymentEnglishAnalysis.charCount} | SMS Count: ${paymentEnglishAnalysis.smsCount} | Fits: ${paymentEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${paymentEnglish}`);

    // Amharic Payment Reminder
    const paymentAmharic = await SmsTemplateService.createPaymentReminderMessage(samplePayment, 'am');
    const paymentAmharicAnalysis = SmsOptimizer.analyzeText(paymentAmharic);
    console.log(`\n✅ Payment Reminder (Amharic)`);
    console.log(`Characters: ${paymentAmharicAnalysis.charCount} | SMS Count: ${paymentAmharicAnalysis.smsCount} | Fits: ${paymentAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${paymentAmharic}`);

    // Test Contract Reminder Templates
    console.log('\n📱 CONTRACT REMINDER TEMPLATES');
    console.log('-'.repeat(50));

    // English Contract Reminder
    const contractEnglish = await SmsTemplateService.createContractReminderMessage(sampleContract, 'en');
    const contractEnglishAnalysis = SmsOptimizer.analyzeText(contractEnglish);
    console.log(`\n✅ Contract Reminder (English)`);
    console.log(`Characters: ${contractEnglishAnalysis.charCount} | SMS Count: ${contractEnglishAnalysis.smsCount} | Fits: ${contractEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${contractEnglish}`);

    // Amharic Contract Reminder
    const contractAmharic = await SmsTemplateService.createContractReminderMessage(sampleContract, 'am');
    const contractAmharicAnalysis = SmsOptimizer.analyzeText(contractAmharic);
    console.log(`\n✅ Contract Reminder (Amharic)`);
    console.log(`Characters: ${contractAmharicAnalysis.charCount} | SMS Count: ${contractAmharicAnalysis.smsCount} | Fits: ${contractAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${contractAmharic}`);

    // Test Service Consistency
    console.log('\n🔄 SERVICE CONSISTENCY TEST');
    console.log('-'.repeat(50));

    // Test that SmsService uses the same templates
    const smsServicePayment = await SmsService.createPaymentReminderMessage(samplePayment, null, 'en');
    const smsServiceContract = await SmsService.createContractReminderMessage(sampleContract, 'en');

    // Test that Schedulers use the same templates
    const schedulerPayment = await PaymentDeadlineScheduler.createPaymentReminderMessage(samplePayment, 1, 'en');
    const schedulerContract = await ContractDeadlineScheduler.createContractReminderMessage(sampleContract, 1, 'en');

    console.log(`\n✅ Template Consistency Check:`);
    console.log(`SmsService Payment == TemplateService Payment: ${smsServicePayment === paymentEnglish ? '✅ MATCH' : '❌ DIFFERENT'}`);
    console.log(`SmsService Contract == TemplateService Contract: ${smsServiceContract === contractEnglish ? '✅ MATCH' : '❌ DIFFERENT'}`);
    console.log(`Scheduler Payment == TemplateService Payment: ${schedulerPayment === paymentEnglish ? '✅ MATCH' : '❌ DIFFERENT'}`);
    console.log(`Scheduler Contract == TemplateService Contract: ${schedulerContract === contractEnglish ? '✅ MATCH' : '❌ DIFFERENT'}`);

    // Test Display Templates
    console.log('\n📱 DISPLAY REMINDER TEMPLATES');
    console.log('-'.repeat(50));

    // English Display Payment Reminder
    const displayPaymentEnglish = await SmsTemplateService.createPaymentDisplayReminderMessage(samplePayment, 'en');
    const displayPaymentEnglishAnalysis = SmsOptimizer.analyzeText(displayPaymentEnglish);
    console.log(`\n✅ Payment Display Reminder (English)`);
    console.log(`Characters: ${displayPaymentEnglishAnalysis.charCount} | SMS Count: ${displayPaymentEnglishAnalysis.smsCount} | Fits: ${displayPaymentEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${displayPaymentEnglish}`);

    // Amharic Display Payment Reminder
    const displayPaymentAmharic = await SmsTemplateService.createPaymentDisplayReminderMessage(samplePayment, 'am');
    const displayPaymentAmharicAnalysis = SmsOptimizer.analyzeText(displayPaymentAmharic);
    console.log(`\n✅ Payment Display Reminder (Amharic)`);
    console.log(`Characters: ${displayPaymentAmharicAnalysis.charCount} | SMS Count: ${displayPaymentAmharicAnalysis.smsCount} | Fits: ${displayPaymentAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${displayPaymentAmharic}`);

    // English Display Contract Reminder
    const displayContractEnglish = await SmsTemplateService.createContractDisplayReminderMessage(sampleContract, 'en');
    const displayContractEnglishAnalysis = SmsOptimizer.analyzeText(displayContractEnglish);
    console.log(`\n✅ Contract Display Reminder (English)`);
    console.log(`Characters: ${displayContractEnglishAnalysis.charCount} | SMS Count: ${displayContractEnglishAnalysis.smsCount} | Fits: ${displayContractEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${displayContractEnglish}`);

    // Amharic Display Contract Reminder
    const displayContractAmharic = await SmsTemplateService.createContractDisplayReminderMessage(sampleContract, 'am');
    const displayContractAmharicAnalysis = SmsOptimizer.analyzeText(displayContractAmharic);
    console.log(`\n✅ Contract Display Reminder (Amharic)`);
    console.log(`Characters: ${displayContractAmharicAnalysis.charCount} | SMS Count: ${displayContractAmharicAnalysis.smsCount} | Fits: ${displayContractAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${displayContractAmharic}`);

    // Summary
    console.log('\n' + '='.repeat(80));
    console.log('CENTRALIZATION TEST SUMMARY');
    console.log('='.repeat(80));

    const allTemplates = [
      paymentEnglishAnalysis,
      paymentAmharicAnalysis,
      contractEnglishAnalysis,
      contractAmharicAnalysis,
      displayPaymentEnglishAnalysis,
      displayPaymentAmharicAnalysis,
      displayContractEnglishAnalysis,
      displayContractAmharicAnalysis
    ];

    const totalTemplates = allTemplates.length;
    const fittingTemplates = allTemplates.filter(t => t.fitsInOneSms).length;

    console.log(`Total Templates Tested: ${totalTemplates}`);
    console.log(`Templates Fitting in 1 SMS: ${fittingTemplates}`);
    console.log(`Templates Requiring Multiple SMS: ${totalTemplates - fittingTemplates}`);
    console.log(`Overall Result: ${fittingTemplates === totalTemplates ? '✅ ALL TEMPLATES OPTIMIZED' : '❌ SOME TEMPLATES NEED OPTIMIZATION'}`);

    console.log('\n🎉 SUCCESS: All SMS templates are now centralized in SmsTemplateService!');
    console.log('📊 All services now use the same optimized templates from a single source.');

  } catch (error) {
    console.error('❌ Error testing centralized templates:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testCentralizedTemplates();
