/**
 * Analyze Consolidated SMS Templates in Display Schedulers
 * Check if consolidated templates exceed SMS limits
 */

const PaymentDisplayDeadlineScheduler = require('../src/schedulers/paymentDisplayDeadlineScheduler');
const ContractDisplayDeadlineScheduler = require('../src/schedulers/contractDisplayDeadlineScheduler');
const SmsTemplateService = require('../src/services/smsTemplateService');
const SmsOptimizer = require('../src/utils/smsOptimizer');

// Sample test data for consolidated messages
const samplePaymentGroup = {
  customer_name: '<PERSON>',
  customer_name_am: 'ዮሐንስ ዶ',
  paymentCount: 3,
  totalAmount: 15000,
  payments: [
    {
      room: '101',
      GroundTotal: 5000,
      end_date: new Date('2024-03-15'),
      days_to_deadline: 5
    },
    {
      room: '102', 
      GroundTotal: 4500,
      end_date: new Date('2024-03-20'),
      days_to_deadline: 10
    },
    {
      room: '103',
      GroundTotal: 5500,
      end_date: new Date('2024-03-25'),
      days_to_deadline: 15
    }
  ]
};

const sampleContractGroup = {
  customer_name: '<PERSON>',
  customer_name_am: 'ዮሐንስ ዶ',
  contractCount: 2,
  totalRent: 8000,
  contracts: [
    {
      RoomID: '101',
      RoomPrice: 3500,
      EndDate: new Date('2024-12-31'),
      days_to_deadline: 30
    },
    {
      RoomID: '102',
      RoomPrice: 4500,
      EndDate: new Date('2024-11-30'),
      days_to_deadline: 15
    }
  ]
};

async function analyzeConsolidatedTemplates() {
  console.log('='.repeat(80));
  console.log('CONSOLIDATED SMS TEMPLATE ANALYSIS');
  console.log('='.repeat(80));
  console.log(`SMS Limits: ${SmsOptimizer.SMS_LIMITS.ENGLISH} English chars, ${SmsOptimizer.SMS_LIMITS.AMHARIC} Amharic chars`);
  console.log('='.repeat(80));

  try {
    // Test Payment Display Consolidated Templates
    console.log('\n📱 PAYMENT DISPLAY CONSOLIDATED TEMPLATES');
    console.log('-'.repeat(60));

    // English Payment Display Consolidated
    const paymentEnglish = await PaymentDisplayDeadlineScheduler.createConsolidatedPaymentDisplayReminderMessage(samplePaymentGroup, 'en');
    const paymentEnglishAnalysis = SmsOptimizer.analyzeText(paymentEnglish);
    console.log(`\n✅ Payment Display Consolidated (English)`);
    console.log(`Characters: ${paymentEnglishAnalysis.charCount} | SMS Count: ${paymentEnglishAnalysis.smsCount} | Fits: ${paymentEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${paymentEnglish}`);
    console.log('\n' + '-'.repeat(40));

    // Amharic Payment Display Consolidated
    const paymentAmharic = await PaymentDisplayDeadlineScheduler.createConsolidatedPaymentDisplayReminderMessage(samplePaymentGroup, 'am');
    const paymentAmharicAnalysis = SmsOptimizer.analyzeText(paymentAmharic);
    console.log(`\n✅ Payment Display Consolidated (Amharic)`);
    console.log(`Characters: ${paymentAmharicAnalysis.charCount} | SMS Count: ${paymentAmharicAnalysis.smsCount} | Fits: ${paymentAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${paymentAmharic}`);
    console.log('\n' + '-'.repeat(40));

    // Test Contract Display Consolidated Templates
    console.log('\n📱 CONTRACT DISPLAY CONSOLIDATED TEMPLATES');
    console.log('-'.repeat(60));

    // English Contract Display Consolidated
    const contractEnglish = await ContractDisplayDeadlineScheduler.createConsolidatedContractDisplayReminderMessage(sampleContractGroup, 'en');
    const contractEnglishAnalysis = SmsOptimizer.analyzeText(contractEnglish);
    console.log(`\n✅ Contract Display Consolidated (English)`);
    console.log(`Characters: ${contractEnglishAnalysis.charCount} | SMS Count: ${contractEnglishAnalysis.smsCount} | Fits: ${contractEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${contractEnglish}`);
    console.log('\n' + '-'.repeat(40));

    // Amharic Contract Display Consolidated
    const contractAmharic = await ContractDisplayDeadlineScheduler.createConsolidatedContractDisplayReminderMessage(sampleContractGroup, 'am');
    const contractAmharicAnalysis = SmsOptimizer.analyzeText(contractAmharic);
    console.log(`\n✅ Contract Display Consolidated (Amharic)`);
    console.log(`Characters: ${contractAmharicAnalysis.charCount} | SMS Count: ${contractAmharicAnalysis.smsCount} | Fits: ${contractAmharicAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${contractAmharic}`);
    console.log('\n' + '-'.repeat(40));

    // Test Direct SmsTemplateService Consolidated Templates
    console.log('\n📱 DIRECT TEMPLATE SERVICE CONSOLIDATED TEMPLATES');
    console.log('-'.repeat(60));

    // Direct SmsTemplateService Payment Consolidated
    const directPaymentEnglish = await SmsTemplateService.createConsolidatedPaymentDisplayReminderMessage(samplePaymentGroup, 'en');
    const directPaymentEnglishAnalysis = SmsOptimizer.analyzeText(directPaymentEnglish);
    console.log(`\n✅ Direct Payment Consolidated (English)`);
    console.log(`Characters: ${directPaymentEnglishAnalysis.charCount} | SMS Count: ${directPaymentEnglishAnalysis.smsCount} | Fits: ${directPaymentEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${directPaymentEnglish}`);

    // Direct SmsTemplateService Contract Consolidated
    const directContractEnglish = await SmsTemplateService.createConsolidatedContractDisplayReminderMessage(sampleContractGroup, 'en');
    const directContractEnglishAnalysis = SmsOptimizer.analyzeText(directContractEnglish);
    console.log(`\n✅ Direct Contract Consolidated (English)`);
    console.log(`Characters: ${directContractEnglishAnalysis.charCount} | SMS Count: ${directContractEnglishAnalysis.smsCount} | Fits: ${directContractEnglishAnalysis.fitsInOneSms ? '✅' : '❌'}`);
    console.log(`Message:\n${directContractEnglish}`);
    console.log('\n' + '-'.repeat(40));

    // Summary
    console.log('\n' + '='.repeat(80));
    console.log('CONSOLIDATED TEMPLATE ANALYSIS SUMMARY');
    console.log('='.repeat(80));

    const allTemplates = [
      { name: 'Payment Display English', analysis: paymentEnglishAnalysis },
      { name: 'Payment Display Amharic', analysis: paymentAmharicAnalysis },
      { name: 'Contract Display English', analysis: contractEnglishAnalysis },
      { name: 'Contract Display Amharic', analysis: contractAmharicAnalysis },
      { name: 'Direct Payment English', analysis: directPaymentEnglishAnalysis },
      { name: 'Direct Contract English', analysis: directContractEnglishAnalysis }
    ];

    const totalTemplates = allTemplates.length;
    const fittingTemplates = allTemplates.filter(t => t.analysis.fitsInOneSms).length;
    const exceedingTemplates = allTemplates.filter(t => !t.analysis.fitsInOneSms);

    console.log(`Total Consolidated Templates: ${totalTemplates}`);
    console.log(`Templates Fitting in 1 SMS: ${fittingTemplates}`);
    console.log(`Templates Requiring Multiple SMS: ${totalTemplates - fittingTemplates}`);

    if (exceedingTemplates.length > 0) {
      console.log('\n❌ TEMPLATES EXCEEDING SMS LIMITS:');
      exceedingTemplates.forEach(template => {
        console.log(`  • ${template.name}: ${template.analysis.charCount} chars (${template.analysis.smsCount} SMS)`);
      });
      console.log('\n🔧 RECOMMENDATION: Optimize consolidated templates to fit SMS limits');
      console.log('   - Remove verbose greetings and closings');
      console.log('   - Abbreviate room labels (R101 instead of Room 101)');
      console.log('   - Shorten currency formatting');
      console.log('   - Use concise action phrases');
    } else {
      console.log('\n✅ ALL CONSOLIDATED TEMPLATES OPTIMIZED');
    }

    console.log(`\nOverall Result: ${fittingTemplates === totalTemplates ? '✅ ALL TEMPLATES FIT IN 1 SMS' : '❌ SOME TEMPLATES NEED OPTIMIZATION'}`);

  } catch (error) {
    console.error('❌ Error analyzing consolidated templates:', error.message);
    console.error(error.stack);
  }
}

// Run the analysis
analyzeConsolidatedTemplates();
