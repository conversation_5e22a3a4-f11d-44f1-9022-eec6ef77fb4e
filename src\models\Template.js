class Template {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.category = data.category;
    this.template_en = data.template_en;
    this.template_am = data.template_am;
    this.variables = data.variables;
    this.usage_count = data.usage_count || 0;
  }

  // Static templates from the existing code
  static getStaticTemplates() {
    return [
      {
        id: 1,
        name: 'Payment Reminder',
        category: 'Payment',
        template_en: `{customerName},
R{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Pay now
ID: {paymentId}`,
        template_am: `{customerName}
ክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
{paymentId}`,
        variables: 'customerName,room,urgencyText,formattedDate,formattedAmount,description,paymentId',
        usage_count: 0
      },
      {
        id: 2,
        name: 'Contract Reminder',
        category: 'Contract',
        template_en: `{customerName},
Your contract for R{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Renew contract
ID: {contractId}`,
        template_am: `{customerName}
ክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
ውል ያድሱ
{contractId}`,
        variables: 'customerName,room,urgencyText,formattedDate,formattedAmount,description,contractId',
        usage_count: 0
      },
      {
        id: 3,
        name: 'Payment Display Reminder',
        category: 'PaymentDisplay',
        template_en: `{customerName},
DR{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Pay now
ID: {paymentId}`,
        template_am: `{customerName}
ማክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
{paymentId}`,
        variables: 'customerName,room,urgencyText,formattedDate,formattedAmount,description,paymentId',
        usage_count: 0
      },
      {
        id: 4,
        name: 'Contract Display Reminder',
        category: 'ContractDisplay',
        template_en: `{customerName},
DR{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Renew contract
ID: {contractId}`,
        template_am: `{customerName}
ማክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
ውል ያድሱ
{contractId}`,
        variables: 'customerName,room,urgencyText,formattedDate,formattedAmount,description,contractId',
        usage_count: 0
      },
      {
        id: 5,
        name: 'Payment Display Consolidated Reminder',
        category: 'payment_display_consolidated_reminder',
        template_en: `{customerName}
{paymentCount} payments:
{paymentDetails}
Total:{formattedTotalAmount}`,
        template_am: `{customerName}
{paymentCount}ክፍያ:
{paymentDetails}
ጠቅላላ:{formattedTotalAmount}`,
        variables: 'customerName,paymentCount,paymentDetails,formattedTotalAmount',
        usage_count: 0
      },
      {
        id: 6,
        name: 'Contract Display Consolidated Reminder',
        category: 'contract_display_consolidated_reminder',
        template_en: `{customerName}
{contractCount} contracts:
{contractDetails}
Total:{formattedTotalRent}`,
        template_am: `{customerName}
{contractCount} ውል:
{contractDetails}
ጠቅላላ:{formattedTotalRent}`,
        variables: 'customerName,contractCount,contractDetails,formattedTotalRent',
        usage_count: 0
      }
    ];
  }

  // Get all templates
  static async findAll(language = null) {
    try {
      const templates = this.getStaticTemplates();

      return templates.map(template => {
        const templateObj = new Template(template);

        // If specific language requested, only return that language template
        if (language === 'en') {
          templateObj.template = templateObj.template_en;
          delete templateObj.template_am;
        } else if (language === 'am') {
          templateObj.template = templateObj.template_am;
          delete templateObj.template_en;
        }

        return templateObj;
      });
    } catch (error) {
      throw new Error(`Error fetching templates: ${error.message}`);
    }
  }

  // Get templates by category
  static async findByCategory(category, language = null) {
    try {
      const templates = this.getStaticTemplates();
      const filteredTemplates = templates.filter(template => template.category === category);

      return filteredTemplates.map(template => {
        const templateObj = new Template(template);

        // If specific language requested, only return that language template
        if (language === 'en') {
          templateObj.template = templateObj.template_en;
          delete templateObj.template_am;
        } else if (language === 'am') {
          templateObj.template = templateObj.template_am;
          delete templateObj.template_en;
        }

        return templateObj;
      });
    } catch (error) {
      throw new Error(`Error fetching templates by category: ${error.message}`);
    }
  }

  // Get template by ID
  static async findById(id, language = null) {
    try {
      const templates = this.getStaticTemplates();
      const template = templates.find(t => t.id === parseInt(id));

      if (!template) {
        return null;
      }

      const templateObj = new Template(template);

      // If specific language requested, only return that language template
      if (language === 'en') {
        templateObj.template = templateObj.template_en;
        delete templateObj.template_am;
      } else if (language === 'am') {
        templateObj.template = templateObj.template_am;
        delete templateObj.template_en;
      }

      return templateObj;
    } catch (error) {
      throw new Error(`Error fetching template by ID: ${error.message}`);
    }
  }

  // Get all available categories
  static async getCategories() {
    try {
      const templates = this.getStaticTemplates();
      const categoryMap = {};

      templates.forEach(template => {
        if (!categoryMap[template.category]) {
          categoryMap[template.category] = 0;
        }
        categoryMap[template.category]++;
      });

      return Object.keys(categoryMap).map(category => ({
        category,
        template_count: categoryMap[category]
      })).sort((a, b) => a.category.localeCompare(b.category));
    } catch (error) {
      throw new Error(`Error fetching template categories: ${error.message}`);
    }
  }

  // Get template statistics
  static async getStatistics() {
    try {
      const templates = this.getStaticTemplates();
      const categories = new Set(templates.map(t => t.category));
      const totalUsage = templates.reduce((sum, t) => sum + t.usage_count, 0);

      return {
        total_templates: templates.length,
        active_templates: templates.length,
        inactive_templates: 0,
        total_categories: categories.size,
        total_usage: totalUsage
      };
    } catch (error) {
      throw new Error(`Error fetching template statistics: ${error.message}`);
    }
  }

  // Increment usage count (static - no persistence)
  static async incrementUsage(id) {
    try {
      // For static templates, we just return success
      // In a real implementation, you might want to store usage in memory or logs
      console.log(`Template ${id} usage incremented`);
      return true;
    } catch (error) {
      throw new Error(`Error incrementing template usage: ${error.message}`);
    }
  }

  // Initialize default templates (static - always available)
  static async initializeDefaults() {
    try {
      return { message: 'Static templates are always available - no initialization needed' };
    } catch (error) {
      throw new Error(`Error initializing default templates: ${error.message}`);
    }
  }
}

module.exports = Template;
