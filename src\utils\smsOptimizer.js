/**
 * SMS Optimizer Utility
 * Helps optimize SMS templates to fit within character limits
 * 1 SMS length: 69 Amharic Characters or 159 English Characters
 */

class SmsOptimizer {
  static SMS_LIMITS = {
    AMHARIC: 69,
    ENGLISH: 159
  };

  /**
   * Count characters in a string
   * @param {string} text - Text to count
   * @returns {number} Character count
   */
  static countCharacters(text) {
    return text.length;
  }

  /**
   * Detect if text contains Amharic characters
   * @param {string} text - Text to analyze
   * @returns {boolean} True if contains Amharic
   */
  static containsAmharic(text) {
    // Amharic Unicode range: U+1200-U+137F
    const amharicRegex = /[\u1200-\u137F]/;
    return amharicRegex.test(text);
  }

  /**
   * Get SMS limit based on text content
   * @param {string} text - Text to analyze
   * @returns {number} Character limit
   */
  static getSmsLimit(text) {
    return this.containsAmharic(text) ? this.SMS_LIMITS.AMHARIC : this.SMS_LIMITS.ENGLISH;
  }

  /**
   * Check if text fits within SMS limits
   * @param {string} text - Text to check
   * @returns {object} Analysis result
   */
  static analyzeText(text) {
    const charCount = this.countCharacters(text);
    const isAmharic = this.containsAmharic(text);
    const limit = isAmharic ? this.SMS_LIMITS.AMHARIC : this.SMS_LIMITS.ENGLISH;
    const fitsInOneSms = charCount <= limit;
    const smsCount = Math.ceil(charCount / limit);

    return {
      charCount,
      isAmharic,
      limit,
      fitsInOneSms,
      smsCount,
      overBy: fitsInOneSms ? 0 : charCount - limit,
      language: isAmharic ? 'Amharic' : 'English'
    };
  }

  /**
   * Remove unnecessary whitespace and line breaks
   * @param {string} text - Text to optimize
   * @returns {string} Optimized text
   */
  static removeExtraWhitespace(text) {
    return text
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Replace triple+ line breaks with double
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .trim();
  }

  /**
   * Optimize template by removing unnecessary characters
   * @param {string} template - Template to optimize
   * @returns {string} Optimized template
   */
  static optimizeTemplate(template) {
    let optimized = template;

    // Remove extra whitespace
    optimized = this.removeExtraWhitespace(optimized);

    // Remove unnecessary punctuation at the end of sentences before line breaks
    optimized = optimized.replace(/\.\s*\n/g, '\n');

    // Shorten common phrases
    const replacements = {
      // English optimizations
      'Please make your payment to avoid any inconvenience.': 'Pay to avoid issues.',
      'Please contact us to renew your contract or arrange move-out procedures.': 'Contact us to renew/move.',
      'Your payment for Room': 'Room',
      'Your rental contract for Room': 'Room',
      'Your building display space payment for Room': 'Display Room',
      'Your building display space contract for Room': 'Display Room',
      'Thank you.': 'Thanks.',
      'Payment ID:': 'ID:',
      'Contract ID:': 'ID:',
      'Dear ': '',
      'Amount: ': '',
      'Description: ': '',
      ' is due ': ' due ',
      ' expires ': ' exp ',
      'Contract Period: ': 'Period: ',
      'Monthly Rent: ': 'Rent: ',

      // Amharic optimizations
      'የእርስዎ የክፍል': 'ክፍል',
      'እናመሰግናለን።': 'እናመሰግናለን',
      'የክፍያ መለያ:': 'መለያ:',
      'የውል መለያ:': 'መለያ:',
      'ውልዎን ለማደስ ወይም የመውጫ ሂደቶችን ለማዘጋጀት እባክዎን ያግኙን።': 'ውልዎን ለማደስ ያግኙን።',
      'የህንፃ ማሳያ ቦታ': 'ማሳያ',
      'ውድ ': '',
      'መጠን: ': '',
      'መግለጫ: ': '',
      'የውል ጊዜ: ': 'ጊዜ: ',
      'ወርሃዊ ኪራይ: ': 'ኪራይ: '
    };

    for (const [original, replacement] of Object.entries(replacements)) {
      optimized = optimized.replace(new RegExp(original, 'g'), replacement);
    }

    return optimized;
  }

  /**
   * Aggressively optimize template to fit within SMS limits
   * @param {string} template - Template to optimize
   * @returns {string} Aggressively optimized template
   */
  static aggressiveOptimize(template) {
    let optimized = this.optimizeTemplate(template);
    const isAmharic = this.containsAmharic(optimized);
    const limit = isAmharic ? this.SMS_LIMITS.AMHARIC : this.SMS_LIMITS.ENGLISH;

    // If still too long, apply more aggressive optimizations
    if (optimized.length > limit) {
      // Remove all empty lines
      optimized = optimized.replace(/\n\s*\n/g, '\n');

      // More aggressive English replacements
      if (!isAmharic) {
        const aggressiveEnglish = {
          'Please renew your contract to avoid any inconvenience': 'Renew contract',
          'Please make your payment to avoid any inconvenience': 'Pay now',
          'Pay to avoid issues.': 'Pay now.',
          'Contact us to renew/move.': 'Call to renew.',
          'Thanks.': '',
          'Room ': 'R',
          ' due ': ' ',
          ' exp ': ' ',
          'Display ': 'D',
          'Period: ': '',
          'Rent: ': ''
        };

        for (const [original, replacement] of Object.entries(aggressiveEnglish)) {
          optimized = optimized.replace(new RegExp(original, 'g'), replacement);
        }
      } else {
        // More aggressive Amharic replacements
        const aggressiveAmharic = {
          'እባክዎ ውልዎን ያድሱ።': 'ውል ያድሱ',
          'ይጠበቃል።': 'ይጠበቃል',
          'ይጠናቀቃል።': 'ይጠናቀቃል',
          'እናመሰግናለን': '',
          'ክፍል ': 'ክ',
          'ክፍያ ': '',
          'ውል ': '',
          'ማሳያ ': 'ማ',
          'ጊዜ: ': '',
          'ኪራይ: ': ''
        };

        for (const [original, replacement] of Object.entries(aggressiveAmharic)) {
          optimized = optimized.replace(new RegExp(original, 'g'), replacement);
        }
      }

      // Final cleanup
      optimized = optimized.replace(/\n+/g, '\n').trim();
    }

    return optimized;
  }

  /**
   * Ultra-aggressive optimization specifically for Amharic templates
   * @param {string} template - Template to optimize
   * @returns {string} Ultra-optimized template
   */
  static ultraOptimizeAmharic(template) {
    let optimized = template;

    // Ultra-aggressive Amharic replacements
    const ultraAmharic = {
      'ይጠበቃል': 'ይጠበቃል',
      'ይጠናቀቃል': 'ይጠናቀቃል',
      'ወርሃዊ ኪራይ ክፍያ': 'ኪራይ',
      'ማሳያ ክ': 'ማክ',
      'ውልዎን ለማደስ ያግኙን': 'ያድሱ',
      'መለያ: ': '',
      '፣': '',
      ' ': ''  // Remove all spaces as last resort
    };

    for (const [original, replacement] of Object.entries(ultraAmharic)) {
      optimized = optimized.replace(new RegExp(original, 'g'), replacement);
    }

    // Remove line breaks if still too long
    if (optimized.length > this.SMS_LIMITS.AMHARIC) {
      optimized = optimized.replace(/\n/g, ' ');
    }

    return optimized.trim();
  }

  /**
   * Analyze and optimize a template
   * @param {string} template - Template to analyze and optimize
   * @returns {object} Analysis and optimization result
   */
  static analyzeAndOptimize(template) {
    const original = this.analyzeText(template);
    const optimized = this.optimizeTemplate(template);
    const optimizedAnalysis = this.analyzeText(optimized);

    return {
      original: {
        text: template,
        analysis: original
      },
      optimized: {
        text: optimized,
        analysis: optimizedAnalysis
      },
      improvement: {
        charsSaved: original.charCount - optimizedAnalysis.charCount,
        smsSaved: original.smsCount - optimizedAnalysis.smsCount
      }
    };
  }

  /**
   * Generate optimization report for multiple templates
   * @param {object} templates - Object with template names as keys and templates as values
   * @returns {object} Comprehensive optimization report
   */
  static generateOptimizationReport(templates) {
    const report = {
      templates: {},
      summary: {
        totalTemplates: 0,
        templatesOptimized: 0,
        totalCharsSaved: 0,
        totalSmsSaved: 0
      }
    };

    for (const [name, template] of Object.entries(templates)) {
      const result = this.analyzeAndOptimize(template);
      report.templates[name] = result;
      
      report.summary.totalTemplates++;
      if (result.improvement.charsSaved > 0) {
        report.summary.templatesOptimized++;
      }
      report.summary.totalCharsSaved += result.improvement.charsSaved;
      report.summary.totalSmsSaved += result.improvement.smsSaved;
    }

    return report;
  }
}

module.exports = SmsOptimizer;
