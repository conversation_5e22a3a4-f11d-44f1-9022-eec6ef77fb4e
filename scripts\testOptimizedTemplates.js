/**
 * Test Optimized SMS Templates
 * Tests all optimized templates with sample data to ensure they fit within SMS limits
 */

const SmsOptimizer = require('../src/utils/smsOptimizer');

// Sample data for testing
const sampleData = {
  customerName: 'John Doe',
  customerNameAm: 'ዮሐንስ ዶ',
  room: '101',
  urgencyText: 'tomorrow',
  urgencyTextAm: 'ነገ',
  formattedDate: '15/03/2024',
  formattedAmount: '5,000 ETB',
  description: 'Monthly rent payment',
  descriptionAm: 'ወርሃዊ ኪራይ ክፍያ',
  paymentId: 'PAY-12345',
  contractId: 'CON-67890',
  formattedStartDate: '01/01/2024',
  formattedEndDate: '31/12/2024',
  formattedPrice: '3,500 ETB'
};

// Optimized templates from Template.js
const optimizedTemplates = {
  'Payment Reminder (English)': `{customerName},
R{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Pay now
ID: {paymentId}`,

  'Payment Reminder (Amharic)': `{customerName}
ክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
{paymentId}`,

  'Contract Reminder (English)': `{customerName},
Your contract for R{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Renew contract
ID: {contractId}`,

  'Contract Reminder (Amharic)': `{customerName}
ክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
ውል ያድሱ
{contractId}`,

  'Payment Display Reminder (English)': `{customerName},
DR{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Pay now
ID: {paymentId}`,

  'Payment Display Reminder (Amharic)': `{customerName}
ማክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
{paymentId}`,

  'Contract Display Reminder (English)': `{customerName},
DR{room} {urgencyText} ({formattedDate})
{formattedAmount}
{description}
Renew contract
ID: {contractId}`,

  'Contract Display Reminder (Amharic)': `{customerName}
ማክ{room} {urgencyText} ({formattedDate})
{formattedAmount}
ውል ያድሱ
{contractId}`,

  'SMS Service Contract Reminder (English)': `{customerName},
R{roomId} exp {urgencyText} ({formattedEndDate})
{formattedStartDate} - {formattedEndDate}
{formattedPrice}
Call to renew
ID: {contractId}`,

  'SMS Service Contract Reminder (Amharic)': `{customerName}
ክ{roomId} {urgencyText} ({formattedEndDate})
{formattedPrice}
ውል ያድሱ
{contractId}`
};

// Function to replace placeholders with sample data
function populateTemplate(template, isAmharic = false) {
  let populated = template;
  
  // Replace placeholders with sample data
  const replacements = {
    '{customerName}': isAmharic ? sampleData.customerNameAm : sampleData.customerName,
    '{room}': sampleData.room,
    '{roomId}': sampleData.room,
    '{urgencyText}': isAmharic ? sampleData.urgencyTextAm : sampleData.urgencyText,
    '{formattedDate}': sampleData.formattedDate,
    '{formattedEndDate}': sampleData.formattedEndDate,
    '{formattedStartDate}': sampleData.formattedStartDate,
    '{formattedAmount}': sampleData.formattedAmount,
    '{formattedPrice}': sampleData.formattedPrice,
    '{description}': isAmharic ? sampleData.descriptionAm : sampleData.description,
    '{paymentId}': sampleData.paymentId,
    '{contractId}': sampleData.contractId
  };

  for (const [placeholder, value] of Object.entries(replacements)) {
    populated = populated.replace(new RegExp(placeholder, 'g'), value);
  }

  return populated;
}

// Test all templates
console.log('='.repeat(80));
console.log('OPTIMIZED SMS TEMPLATE TESTING');
console.log('='.repeat(80));
console.log(`SMS Limits: ${SmsOptimizer.SMS_LIMITS.ENGLISH} English chars, ${SmsOptimizer.SMS_LIMITS.AMHARIC} Amharic chars`);
console.log('='.repeat(80));

let allPassed = true;
let totalTemplates = 0;
let passedTemplates = 0;

for (const [templateName, template] of Object.entries(optimizedTemplates)) {
  totalTemplates++;
  const isAmharic = templateName.includes('Amharic');
  const populatedTemplate = populateTemplate(template, isAmharic);
  const analysis = SmsOptimizer.analyzeText(populatedTemplate);
  
  console.log(`\n📱 ${templateName}`);
  console.log('-'.repeat(50));
  console.log(`Characters: ${analysis.charCount} (${analysis.language})`);
  console.log(`SMS Count: ${analysis.smsCount}`);
  console.log(`Fits in 1 SMS: ${analysis.fitsInOneSms ? '✅ YES' : '❌ NO'}`);
  
  if (!analysis.fitsInOneSms) {
    console.log(`❌ Over by: ${analysis.overBy} characters`);
    allPassed = false;
  } else {
    passedTemplates++;
  }
  
  console.log('\nPopulated Template:');
  console.log('```');
  console.log(populatedTemplate);
  console.log('```');
}

// Summary
console.log('\n' + '='.repeat(80));
console.log('TESTING SUMMARY');
console.log('='.repeat(80));
console.log(`Total Templates Tested: ${totalTemplates}`);
console.log(`Templates Fitting in 1 SMS: ${passedTemplates}`);
console.log(`Templates Requiring Multiple SMS: ${totalTemplates - passedTemplates}`);
console.log(`Overall Result: ${allPassed ? '✅ ALL TEMPLATES OPTIMIZED' : '⚠️ SOME TEMPLATES NEED MORE OPTIMIZATION'}`);
console.log('='.repeat(80));

if (allPassed) {
  console.log('\n🎉 SUCCESS: All SMS templates have been optimized to fit within SMS limits!');
  console.log('📊 Character savings achieved:');
  console.log('   - Removed unnecessary greetings and closings');
  console.log('   - Shortened common phrases');
  console.log('   - Eliminated extra whitespace');
  console.log('   - Used abbreviations where appropriate');
} else {
  console.log('\n⚠️ Some templates still need optimization. Consider:');
  console.log('   - Further shortening variable content');
  console.log('   - Using more abbreviations');
  console.log('   - Removing non-essential information');
}
