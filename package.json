{"name": "smsgateways", "version": "1.0.0", "description": "SMS Gateway System for Payment and Contract Deadline Notifications", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate:sms": "node migrate-sms-settings.js", "test:sms": "node test_sms.js", "example:sms": "node sms-usage-example.js", "init:languages": "node initialize-languages.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.12.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.17.1", "express-validator": "^6.12.2", "helmet": "^4.6.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "mssql": "^6.3.1", "node-cron": "^2.0.3", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}