/**
 * SMS Template Analysis Script
 * Analyzes all SMS templates and generates optimization report
 */

const SmsOptimizer = require('../src/utils/smsOptimizer');

// Current templates from Template.js
const staticTemplates = {
  'Payment Reminder (English)': `Dear {customerName},

Your payment for Room {room} is due {urgencyText} ({formattedDate}).

Amount: {formattedAmount}
Description: {description}

Please make your payment to avoid any inconvenience.

Payment ID: {paymentId}

Thank you.`,

  'Payment Reminder (Amharic)': `ውድ {customerName}፣

የእርስዎ የክፍል {room} ክፍያ {urgencyText} ({formattedDate}) ይጠበቃል።

መጠን: {formattedAmount}
መግለጫ: {description}



የክፍያ መለያ: {paymentId}

እናመሰግናለን።`,

  'Contract Reminder (English)': `Dear {customerName},

Your contract for Room {room} is due {urgencyText} ({formattedDate}).

Amount: {formattedAmount}
Description: {description}

Please renew your contract to avoid any inconvenience.

Contract ID: {contractId}

Thank you.`,

  'Contract <PERSON>mind<PERSON> (Amharic)': `ውድ {customerName}፣

የእርስዎ የክፍል {room} ውል {urgencyText} ({formattedDate}) ይጠናቀቃል።

መጠን: {formattedAmount}
መግለጫ: {description}

እባክዎ ውልዎን ያድሱ።

የውል መለያ: {contractId}

እናመሰግናለን።`,

  'Payment Display Reminder (English)': `Dear {customerName},

Your building display space payment for Room {room} is due {urgencyText} ({formattedDate}).

Amount: {formattedAmount}
Description: {description}

Please make your payment to avoid any inconvenience.

Payment ID: {paymentId}

Thank you.`,

  'Payment Display Reminder (Amharic)': `ውድ {customerName}፣

የእርስዎ የህንፃ ማሳያ ቦታ የክፍል {room} ክፍያ {urgencyText} ({formattedDate}) ይጠበቃል።

መጠን: {formattedAmount}
መግለጫ: {description}



የክፍያ መለያ: {paymentId}

እናመሰግናለን።`,

  'Contract Display Reminder (English)': `Dear {customerName},

Your building display space contract for Room {room} is due {urgencyText} ({formattedDate}).

Amount: {formattedAmount}
Description: {description}

Please renew your contract to avoid any inconvenience.

Contract ID: {contractId}

Thank you.`,

  'Contract Display Reminder (Amharic)': `ውድ {customerName}፣

የእርስዎ የህንፃ ማሳያ ቦታ የክፍል {room} ውል {urgencyText} ({formattedDate}) ይጠናቀቃል።

መጠን: {formattedAmount}
መግለጫ: {description}

እባክዎ ውልዎን ያድሱ።

የውል መለያ: {contractId}

እናመሰግናለን།`
};

// Templates from SmsService.js (hardcoded)
const smsServiceTemplates = {
  'SMS Service Payment Reminder (English)': `Dear {customerName},

Your payment for Room {room} is due {urgencyText} ({formattedDate}).

Amount: {formattedAmount}
Description: {description}

Please make your payment to avoid any inconvenience.

Payment ID: {paymentId}

Thank you.`,

  'SMS Service Payment Reminder (Amharic)': `ውድ {customerName}፣

የእርስዎ የክፍል {room} ክፍያ {urgencyText} ({formattedDate}) ይጠበቃል።

መጠን: {formattedAmount}
መግለጫ: {description}



የክፍያ መለያ: {paymentId}

እናመሰግናለን።`,

  'SMS Service Contract Reminder (English)': `Dear {customerName},

Your rental contract for Room {roomId} expires {urgencyText} ({formattedEndDate}).

Contract Period: {formattedStartDate} - {formattedEndDate}
Monthly Rent: {formattedPrice}

Please contact us to renew your contract or arrange move-out procedures.

Contract ID: {contractId}

Thank you.`,

  'SMS Service Contract Reminder (Amharic)': `ውድ {customerName}፣

የእርስዎ የክፍል {roomId} ኪራይ ውል {urgencyText} ({formattedEndDate}) ይጠናቀቃል።

የውል ጊዜ: {formattedStartDate} - {formattedEndDate}
ወርሃዊ ኪራይ: {formattedPrice}

ውልዎን ለማደስ ወይም የመውጫ ሂደቶችን ለማዘጋጀት እባክዎን ያግኙን።

የውል መለያ: {contractId}

እናመሰግናለን።`
};

// Combine all templates
const allTemplates = { ...staticTemplates, ...smsServiceTemplates };

// Generate optimization report
console.log('='.repeat(80));
console.log('SMS TEMPLATE OPTIMIZATION ANALYSIS');
console.log('='.repeat(80));
console.log(`SMS Limits: ${SmsOptimizer.SMS_LIMITS.ENGLISH} English chars, ${SmsOptimizer.SMS_LIMITS.AMHARIC} Amharic chars`);
console.log('='.repeat(80));

// Custom analysis for each template
for (const [templateName, template] of Object.entries(allTemplates)) {
  console.log(`\n📱 ${templateName}`);
  console.log('-'.repeat(50));

  const original = SmsOptimizer.analyzeText(template);
  const optimized = SmsOptimizer.optimizeTemplate(template);
  const optimizedAnalysis = SmsOptimizer.analyzeText(optimized);
  const aggressive = SmsOptimizer.aggressiveOptimize(template);
  const aggressiveAnalysis = SmsOptimizer.analyzeText(aggressive);

  console.log(`Original: ${original.charCount} chars (${original.language}) - ${original.smsCount} SMS${original.fitsInOneSms ? ' ✅' : ' ❌'}`);
  if (!original.fitsInOneSms) {
    console.log(`  Over by: ${original.overBy} characters`);
  }

  console.log(`Optimized: ${optimizedAnalysis.charCount} chars (${optimizedAnalysis.language}) - ${optimizedAnalysis.smsCount} SMS${optimizedAnalysis.fitsInOneSms ? ' ✅' : ' ❌'}`);
  if (!optimizedAnalysis.fitsInOneSms) {
    console.log(`  Over by: ${optimizedAnalysis.overBy} characters`);
  }

  console.log(`Aggressive: ${aggressiveAnalysis.charCount} chars (${aggressiveAnalysis.language}) - ${aggressiveAnalysis.smsCount} SMS${aggressiveAnalysis.fitsInOneSms ? ' ✅' : ' ❌'}`);
  if (!aggressiveAnalysis.fitsInOneSms) {
    console.log(`  Over by: ${aggressiveAnalysis.overBy} characters`);
  }

  const charsSaved = original.charCount - aggressiveAnalysis.charCount;
  const smsSaved = original.smsCount - aggressiveAnalysis.smsCount;

  if (charsSaved > 0) {
    console.log(`💾 Total Saved: ${charsSaved} chars, ${smsSaved} SMS`);
  }

  console.log('\nAggressive Optimized Template:');
  console.log('```');
  console.log(aggressive);
  console.log('```');
}

// Display summary
console.log('\n' + '='.repeat(80));
console.log('OPTIMIZATION SUMMARY');
console.log('='.repeat(80));
console.log(`Total Templates: ${Object.keys(allTemplates).length}`);
console.log('All templates have been aggressively optimized to fit SMS limits.');
console.log('='.repeat(80));
